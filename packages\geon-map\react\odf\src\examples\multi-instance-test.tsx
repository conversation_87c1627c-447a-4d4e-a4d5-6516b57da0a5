"use client";

import React from "react";

import { Map } from "../components/map";
import { ControlsConfigProvider } from "../contexts/controls-config-context";
import { MapInstanceProvider } from "../contexts/map-instance-context";
import { BasemapProvider } from "../providers/map/basemap-provider";
import { ClearProvider } from "../providers/draw/clear-provider";
import { ControlsProvider } from "../providers/controls-provider";
import { DrawProvider } from "../providers/draw/draw-provider";
import { MapProvider } from "../providers/map-provider";
import { MeasureProvider } from "../providers/draw/measure-provider";
import { ScaleProvider } from "../providers/map/scale-provider";

/**
 * 🧪 다중 지도 인스턴스 테스트 컴포넌트
 * 
 * 새로운 ControlsProvider 아키텍처를 테스트합니다.
 * 각 지도 인스턴스가 독립적인 Store에 Control을 저장하는지 확인합니다.
 */

// 🎯 방법 1: 새로운 통합 ControlsProvider 사용 (권장)
export function MultiInstanceTestNew() {
  return (
    <MapProvider 
      defaultOptions={{ 
        projection: "EPSG:5186",
        center: [899587.**********, 1664175.**********],
        zoom: 8,
      }}
      enableMultiInstance
    >
      <div className="flex gap-4 h-screen">
        {/* 첫 번째 지도 */}
        <div className="flex-1">
          <h2 className="text-lg font-bold mb-2">지도 1 (서울)</h2>
          <MapInstanceProvider mapId="seoul-map">
            <ControlsProvider
              drawOptions={{
                tools: ["polygon", "point", "lineString"],
                style: {
                  fill: { color: [255, 0, 0, 0.3] },
                  stroke: { color: [255, 0, 0, 1], width: 2 },
                },
              }}
              measureOptions={{
                tools: ["distance", "area"],
                style: {
                  fill: { color: [0, 255, 0, 0.3] },
                  stroke: { color: [0, 255, 0, 1], width: 2 },
                },
              }}
              basemapOptions={{}}
              scaleOptions={{ size: 100 }}
            >
              <Map 
                className="w-full h-96 border"
                center={[953977, 1952062]} // 서울
                zoom={10}
              />
              <div className="mt-2 space-x-2">
                <button className="px-3 py-1 bg-red-500 text-white rounded">
                  Draw Polygon
                </button>
                <button className="px-3 py-1 bg-green-500 text-white rounded">
                  Measure Distance
                </button>
                <button className="px-3 py-1 bg-gray-500 text-white rounded">
                  Clear All
                </button>
              </div>
            </ControlsProvider>
          </MapInstanceProvider>
        </div>

        {/* 두 번째 지도 */}
        <div className="flex-1">
          <h2 className="text-lg font-bold mb-2">지도 2 (부산)</h2>
          <MapInstanceProvider mapId="busan-map">
            <ControlsProvider
              drawOptions={{
                tools: ["circle", "box", "curve"],
                style: {
                  fill: { color: [0, 0, 255, 0.3] },
                  stroke: { color: [0, 0, 255, 1], width: 3 },
                },
              }}
              measureOptions={{
                tools: ["area", "round"],
                style: {
                  fill: { color: [255, 255, 0, 0.3] },
                  stroke: { color: [255, 255, 0, 1], width: 3 },
                },
              }}
              basemapOptions={{}}
              scaleOptions={{ size: 150 }}
            >
              <Map 
                className="w-full h-96 border"
                center={[1102576, 1716026]} // 부산
                zoom={11}
              />
              <div className="mt-2 space-x-2">
                <button className="px-3 py-1 bg-blue-500 text-white rounded">
                  Draw Circle
                </button>
                <button className="px-3 py-1 bg-yellow-500 text-white rounded">
                  Measure Area
                </button>
                <button className="px-3 py-1 bg-gray-500 text-white rounded">
                  Clear All
                </button>
              </div>
            </ControlsProvider>
          </MapInstanceProvider>
        </div>
      </div>
    </MapProvider>
  );
}

// 🎯 방법 2: 기존 Provider 구조 사용 (하위 호환성)
export function MultiInstanceTestLegacy() {
  return (
    <MapProvider 
      defaultOptions={{ 
        projection: "EPSG:5186",
        center: [899587.**********, 1664175.**********],
        zoom: 8,
      }}
      enableMultiInstance
    >
      <div className="flex gap-4 h-screen">
        {/* 첫 번째 지도 */}
        <div className="flex-1">
          <h2 className="text-lg font-bold mb-2">지도 1 (기존 방식)</h2>
          <MapInstanceProvider mapId="legacy-map-1">
            <ControlsConfigProvider>
              <ControlsProvider>
                <DrawProvider 
                  drawOptions={{
                    tools: ["polygon", "point"],
                    style: {
                      fill: { color: [255, 0, 0, 0.3] },
                      stroke: { color: [255, 0, 0, 1], width: 2 },
                    },
                  }}
                />
                <MeasureProvider 
                  measureOptions={{
                    tools: ["distance", "area"],
                  }}
                />
                <ClearProvider />
                <BasemapProvider />
                <ScaleProvider scaleOptions={{ size: 100 }} />
                
                <Map 
                  className="w-full h-96 border"
                  center={[953977, 1952062]}
                  zoom={10}
                />
              </ControlsProvider>
            </ControlsConfigProvider>
          </MapInstanceProvider>
        </div>

        {/* 두 번째 지도 */}
        <div className="flex-1">
          <h2 className="text-lg font-bold mb-2">지도 2 (기존 방식)</h2>
          <MapInstanceProvider mapId="legacy-map-2">
            <ControlsConfigProvider>
              <ControlsProvider>
                <DrawProvider 
                  drawOptions={{
                    tools: ["circle", "box"],
                    style: {
                      fill: { color: [0, 0, 255, 0.3] },
                      stroke: { color: [0, 0, 255, 1], width: 3 },
                    },
                  }}
                />
                <MeasureProvider 
                  measureOptions={{
                    tools: ["area", "round"],
                  }}
                />
                <ClearProvider />
                <BasemapProvider />
                <ScaleProvider scaleOptions={{ size: 150 }} />
                
                <Map 
                  className="w-full h-96 border"
                  center={[1102576, 1716026]}
                  zoom={11}
                />
              </ControlsProvider>
            </ControlsConfigProvider>
          </MapInstanceProvider>
        </div>
      </div>
    </MapProvider>
  );
}

// 🎯 방법 3: 단일 지도 (기존 동작 확인)
export function SingleInstanceTest() {
  return (
    <MapProvider 
      defaultOptions={{ 
        projection: "EPSG:5186",
        center: [899587.**********, 1664175.**********],
        zoom: 8,
      }}
    >
      <ControlsConfigProvider>
        <ControlsProvider>
          <DrawProvider 
            drawOptions={{
              tools: ["polygon", "point", "lineString"],
              style: {
                fill: { color: [255, 0, 0, 0.3] },
                stroke: { color: [255, 0, 0, 1], width: 2 },
              },
            }}
          />
          <MeasureProvider 
            measureOptions={{
              tools: ["distance", "area"],
            }}
          />
          <ClearProvider />
          <BasemapProvider />
          <ScaleProvider scaleOptions={{ size: 100 }} />
          
          <div className="p-4">
            <h2 className="text-lg font-bold mb-2">단일 지도 테스트</h2>
            <Map 
              className="w-full h-96 border"
              center={[899587.**********, 1664175.**********]}
              zoom={8}
            />
            <div className="mt-2 space-x-2">
              <button className="px-3 py-1 bg-red-500 text-white rounded">
                Draw Polygon
              </button>
              <button className="px-3 py-1 bg-green-500 text-white rounded">
                Measure Distance
              </button>
              <button className="px-3 py-1 bg-gray-500 text-white rounded">
                Clear All
              </button>
            </div>
          </div>
        </ControlsProvider>
      </ControlsConfigProvider>
    </MapProvider>
  );
}

/**
 * 🧪 테스트 시나리오
 * 
 * 1. MultiInstanceTestNew: 새로운 통합 ControlsProvider 사용
 *    - 각 지도가 독립적인 Store에 Control 저장
 *    - 서로 다른 스타일과 도구 설정
 * 
 * 2. MultiInstanceTestLegacy: 기존 Provider 구조 사용
 *    - 하위 호환성 확인
 *    - Config Provider를 통한 설정 전달
 * 
 * 3. SingleInstanceTest: 단일 지도 동작 확인
 *    - 기존 동작이 그대로 작동하는지 확인
 */
