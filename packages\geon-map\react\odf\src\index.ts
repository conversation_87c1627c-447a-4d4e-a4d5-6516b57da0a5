"use client";

// Components
export { Map } from "./components/map";
export { Popup } from "./components/popup";

// Hooks
export { useDownload } from "./hooks/use-download";
export { useDraw } from "./hooks/use-draw";
export { useFeature } from "./hooks/use-feature";
export {
  createStylePreset,
  STYLE_PRESETS,
  useFeatureActions,
} from "./hooks/use-feature-actions";
export {
  createLayerMetadata,
  determineLayerTypeByODFId,
  getODFLayerId,
  useLayer,
} from "./hooks/use-layer";
export { useMap } from "./hooks/use-map";
export { useMapActions } from "./hooks/use-map-actions";
export {
  useMapInstance,
  useMapInstanceRequired,
} from "./hooks/use-map-instance";
export { useOverview } from "./hooks/use-overview";
export { useProjection } from "./hooks/use-projection";
export { useScale } from "./hooks/use-scale";

// Draw Types
export type { UseDrawNewOptions } from "./hooks/use-draw";

// 🆕 통합 Controls Provider (권장)
export {
  type ControlsProviderOptions,
  ControlsProvider,
} from "./providers/controls-provider";

// New Providers (recommended)
export {
  type DrawProviderOptions,
  DrawProvider,
  useDrawProviderStatus,
} from "./providers/draw/draw-provider";
export {
  type OverviewProviderOptions,
  OverviewProvider,
} from "./providers/map/overview-provider";

// 세분화된 Control Provider들 (Config 전용)
export {
  type ClearProviderOptions,
  ClearProvider,
} from "./providers/draw/clear-provider";
export {
  type MeasureProviderOptions,
  MeasureProvider,
} from "./providers/draw/measure-provider";
export {
  type BasemapProviderOptions,
  BasemapProvider,
} from "./providers/map/basemap-provider";
export {
  type ScaleProviderOptions,
  ScaleProvider,
} from "./providers/map/scale-provider";
export {
  MapProvider,
  useMapConfig,
  useMapProviderStatus,
} from "./providers/map-provider";

// Contexts (🆕 다중 지도 인스턴스 지원)
export {
  type MapInstanceContextType,
  type MapInstanceInitialOptions,
  MapInstanceProvider,
  useIsMultiInstance,
  useMapId,
  useMapInstanceContext,
  useMapInstanceContextRequired,
  useMapInstanceDebugInfo,
} from "./contexts/map-instance-context";

// 🆕 Controls Config Context
export {
  type ControlsConfigContextType,
  ControlsConfigProvider,
  useControlsConfig,
  useControlsConfigProviderStatus,
} from "./contexts/controls-config-context";

// Stores
export {
  createDrawStore, // 🆕 팩토리 함수
  useDrawActions,
  useDrawState,
  useDrawStore,
} from "./stores/draw-store";
export {
  createEventStore, // 🆕 팩토리 함수
  useEventState,
  useEventStoreActions,
} from "./stores/event-store";
export {
  createLayerStore, // 🆕 팩토리 함수
  useLayerStore,
} from "./stores/layer-store";
export {
  createMapStore, // 🆕 팩토리 함수
  useMapState,
  useMapStore,
  useMapStoreActions,
} from "./stores/map-store"; // 새로운 통합 Store
export { logger } from "./stores/middleware/logger";
// Event Management
export {
  type LayerDetectionResult,
  type LayerFeatureInfo,
  type LayerQueueItem,
  useEvent,
} from "./hooks/use-event";

// Utils
export {
  changeFeatureStyle,
  deleteFeature,
  extractFeatureId,
  validateStyleOptions,
} from "./utils/feature-actions";

// Types
export type * from "./types/event-types";
export type * from "./types/layer-types";
export type * from "./types/map-types";
export type * from "./types/marker-types";
export type * from "./types/popup-types";

// Explicit exports for commonly used types
export type { BasemapId } from "./types/map-types";
