"use client";

import type { ReactNode } from "react";
import React, { createContext, useContext } from "react";

import type { DrawControlOptions } from "../types/draw-types";

// 측정 옵션 타입은 core에서 직접 관리하므로 any로 수용
type MeasureControlOptions = any;

/**
 * Controls 설정 컨텍스트 타입
 */
export interface ControlsConfigContextType {
  drawOptions?: DrawControlOptions;
  measureOptions?: MeasureControlOptions;
  clearOptions?: {
    clearAll?: boolean;
  };
  basemapOptions?: {
    basemapList?: any;
    urls?: any;
  };
  scaleOptions?: {
    size?: number;
    scaleInput?: boolean;
  };
  overviewOptions?: {
    enabled?: boolean;
  };
  autoInitialize?: boolean;
  onError?: (error: Error) => void;
}

// Controls Config Context 생성
const ControlsConfigContext = createContext<ControlsConfigContextType | null>(null);

/**
 * Controls 설정 Provider
 *
 * 각 Control Provider들이 설정한 옵션들을 수집하여 ControlsProvider에 전달합니다.
 */
export interface ControlsConfigProviderProps {
  children: ReactNode;
  initialConfig?: ControlsConfigContextType;
}

export function ControlsConfigProvider({
  children,
  initialConfig = {},
}: ControlsConfigProviderProps) {
  const [config, setConfig] = React.useState<ControlsConfigContextType>(initialConfig);

  const updateConfig = React.useCallback((updates: Partial<ControlsConfigContextType>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  }, []);

  const contextValue = React.useMemo(() => ({
    ...config,
    updateConfig,
  }), [config, updateConfig]);

  return (
    <ControlsConfigContext.Provider value={contextValue}>
      {children}
    </ControlsConfigContext.Provider>
  );
}

/**
 * Controls 설정에 접근하는 훅
 */
export function useControlsConfig(): ControlsConfigContextType & {
  updateConfig: (updates: Partial<ControlsConfigContextType>) => void;
} {
  const context = useContext(ControlsConfigContext);

  if (!context) {
    // 기본값 반환 (fallback)
    return {
      drawOptions: undefined,
      measureOptions: undefined,
      clearOptions: undefined,
      basemapOptions: undefined,
      scaleOptions: undefined,
      overviewOptions: undefined,
      autoInitialize: true,
      onError: undefined,
      updateConfig: () => {
        console.warn("⚠️ ControlsConfigProvider가 설정되지 않았습니다.");
      },
    };
  }

  return context as any;
}

/**
 * Controls Config Provider 설정 여부를 확인하는 훅
 */
export function useControlsConfigProviderStatus(): {
  isProviderSet: boolean;
  hasWarned: boolean;
} {
  const context = useContext(ControlsConfigContext);

  return {
    isProviderSet: !!context,
    hasWarned: !context && process.env.NODE_ENV === "development",
  };
}
