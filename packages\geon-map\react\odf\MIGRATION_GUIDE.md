# 🗺️ 다중 지도 인스턴스 마이그레이션 가이드

이 가이드는 OpenLayers 기반 지도 라이브러리에서 **다중 지도 인스턴스** 기능을 사용하는 방법을 설명합니다.

## 🎯 개요

기존 버전은 전역 Zustand 스토어를 사용하여 단일 지도만 지원했지만, 새 버전에서는 **인스턴스별 독립 상태 관리**를 통해 여러 개의 지도를 동시에 렌더링할 수 있습니다.

## 🔄 변경사항

### 이전 (단일 지도만 지원)
```tsx
// ❌ 두 개의 지도가 같은 상태를 공유함
<MapProvider defaultOptions={...}>
  <Map center={[127, 37]}>
    <DrawProvider />
  </Map>
  <Map center={[129, 35]}> {/* 상태 충돌 발생! */}
    <DrawProvider />
  </Map>
</MapProvider>
```

### 이후 (다중 지도 지원)
```tsx
// ✅ 각 지도가 독립적인 상태를 가짐
<MapProvider defaultOptions={...}>
  <Map mapId="map1" enableMultiInstance center={[127, 37]}>
    <DrawProvider />
  </Map>
  <Map mapId="map2" enableMultiInstance center={[129, 35]}>
    <DrawProvider />
  </Map>
</MapProvider>
```

## 🚀 사용법

### 1. 기존 코드 (변경 없음)
기존 단일 지도 코드는 **그대로 동작**합니다:

```tsx
<MapProvider defaultOptions={{ projection: 'EPSG:4326' }}>
  <Map className="w-full h-96">
    <DrawProvider />
  </Map>
</MapProvider>
```

### 2. 다중 지도 인스턴스 (신규)

#### 기본 사용법
```tsx
<MapProvider defaultOptions={{ projection: 'EPSG:4326' }}>
  {/* 첫 번째 지도 */}
  <Map 
    mapId="seoul-map" 
    enableMultiInstance 
    center={[953977, 1952062]}
    zoom={10}
  >
    <DrawProvider />
  </Map>
  
  {/* 두 번째 지도 */}
  <Map 
    mapId="busan-map" 
    enableMultiInstance 
    center={[1102576, 1716026]}
    zoom={11}
  >
    <DrawProvider />
  </Map>
</MapProvider>
```

#### 고급 사용법 (초기 스토어 옵션 설정)
```tsx
<Map 
  mapId="custom-map"
  enableMultiInstance
  center={[127, 37]}
  initialStoreOptions={{
    map: {
      center: [127, 37],
      zoom: 10,
    },
    draw: {
      drawStyle: {
        fill: { color: [255, 0, 0, 0.3] },
        stroke: { color: [255, 0, 0, 1], width: 2 },
      }
    }
  }}
>
  <DrawProvider />
</Map>
```

### 3. 컴포넌트에서 상태 접근

다중 지도 환경에서도 기존 훅들이 그대로 동작합니다:

```tsx
function MapControls() {
  const { isReady, center, zoom } = useMap();
  const { setCenter, setZoom } = useMapActions();
  const { startDrawing } = useDraw();
  
  // 현재 지도의 ID 확인
  const mapId = useMapId();
  const isMultiInstance = useIsMultiInstance();
  
  return (
    <div>
      <p>Map ID: {mapId}</p>
      <p>Multi Instance: {isMultiInstance ? 'Yes' : 'No'}</p>
      <p>Center: {center.join(', ')}</p>
      <button onClick={() => setCenter([129, 35])}>
        Move to Busan
      </button>
    </div>
  );
}
```

## 🧪 디버깅 및 개발 도구

### 지도 상태 모니터링
```tsx
import { useMapInstanceDebugInfo } from '@geon-map/react-odf';

function MapDebugInfo() {
  const debugInfo = useMapInstanceDebugInfo();
  
  console.log('Map Debug Info:', debugInfo);
  /*
  {
    hasContext: true,
    mapId: "seoul-map",
    isMultiInstance: true,
    storesInitialized: {
      mapStore: true,
      eventStore: true,
      layerStore: true,
      drawStore: true
    }
  }
  */
  
  return <div>Map ID: {debugInfo.mapId}</div>;
}
```

### 개발자 도구에서 스토어 확인
브라우저 개발자 도구에서 각 지도 인스턴스별로 Redux DevTools를 통해 상태를 확인할 수 있습니다.

## ⚠️ 주의사항

### 1. mapId 고유성
각 지도는 고유한 `mapId`를 가져야 합니다:

```tsx
// ❌ 잘못된 예 - 같은 mapId
<Map mapId="map1" enableMultiInstance>...</Map>
<Map mapId="map1" enableMultiInstance>...</Map> {/* 충돌! */}

// ✅ 올바른 예 - 고유한 mapId
<Map mapId="map1" enableMultiInstance>...</Map>
<Map mapId="map2" enableMultiInstance>...</Map>
```

### 2. 메모리 관리
지도 컴포넌트가 언마운트될 때 스토어가 자동으로 정리됩니다. 동적으로 지도를 생성/제거하는 경우 메모리 누수에 주의하세요.

### 3. 성능 고려사항
- 동시에 렌더링하는 지도 개수가 많으면 성능에 영향을 줄 수 있습니다
- 필요에 따라 조건부 렌더링을 고려하세요

```tsx
// 조건부 렌더링으로 성능 최적화
{selectedMapId === 'map1' && (
  <Map mapId="map1" enableMultiInstance>...</Map>
)}
{selectedMapId === 'map2' && (
  <Map mapId="map2" enableMultiInstance>...</Map>
)}
```

## 🔧 API 참조

### Map 컴포넌트 Props
```tsx
interface MapProps {
  // 기존 props
  className?: string;
  style?: React.CSSProperties;
  children: React.ReactNode;
  center?: [number, number];
  zoom?: number;
  projection?: string;
  
  // 🆕 다중 인스턴스 props
  mapId?: string;
  enableMultiInstance?: boolean;
  initialStoreOptions?: MapInstanceInitialOptions;
}
```

### 새로운 훅들
```tsx
// 현재 지도 ID 조회
const mapId = useMapId(); // 'map1' | 'map2' | 'global'

// 다중 인스턴스 모드 확인
const isMulti = useIsMultiInstance(); // boolean

// 디버그 정보
const debugInfo = useMapInstanceDebugInfo();
```

## 📝 마이그레이션 체크리스트

- [ ] 기존 단일 지도 코드가 여전히 작동하는가?
- [ ] 다중 지도 사용 시 각 지도가 독립적인 상태를 가지는가?
- [ ] 지도별 mapId가 고유한가?
- [ ] 메모리 누수가 없는가?
- [ ] 성능 문제가 없는가?

## 🆘 문제 해결

### Q: 기존 코드가 작동하지 않아요
A: 기존 코드는 그대로 작동해야 합니다. `enableMultiInstance=true`를 추가하지 않으면 기존 방식으로 동작합니다.

### Q: 두 지도가 같은 상태를 공유해요
A: `enableMultiInstance={true}`와 고유한 `mapId`를 설정했는지 확인하세요.

### Q: 성능이 느려요
A: 동시에 렌더링하는 지도 개수를 줄이거나 조건부 렌더링을 사용하세요.

## 📚 추가 자료

- [다중 지도 예제](./src/examples/multi-map-example.tsx)
- [API 문서](./API.md)
- [성능 최적화 가이드](./PERFORMANCE.md)