"use client";

import { ODF, ODF_MAP } from "@geon-map/core";
import { QueryProvider } from "@geon-query/react-query/";
import React, { createContext, ReactNode, useContext } from "react";

import {
  type MapInstanceInitialOptions,
  MapInstanceProvider,
} from "../contexts/map-instance-context";
import type { MapInitializeOptions } from "../types/map-types";

// 전역 지도 설정 Context
interface MapConfigContextType {
  defaultOptions: Partial<MapInitializeOptions>;
  onMapReady?: (map: ODF_MAP, odf: ODF) => void;
}

const MapConfigContext = createContext<MapConfigContextType | null>(null);

/**
 * 지도 전역 설정 Provider
 *
 * 앱 전체에서 사용할 기본 지도 설정을 제공합니다.
 * 개별 Map 컴포넌트에서 props로 오버라이드 가능합니다.
 *
 * @example
 * ```tsx
 * // layout.tsx
 * <MapProvider
 *   defaultOptions={{
 *     projection: "EPSG:4326",
 *     center: [126.9780, 37.5665],
 *     zoom: 10,
 *     basemap: "vworld_base"
 *   }}
 * >
 *   <LayerProvider>
 *     <DrawProvider>
 *       {children}
 *     </DrawProvider>
 *   </LayerProvider>
 * </MapProvider>
 * ```
 */

interface MapProviderProps {
  children: ReactNode;
  defaultOptions: Partial<MapInitializeOptions>;
  onMapReady?: (map: any, odf: any) => void;

  // 🆕 다중 지도 인스턴스 지원 옵션들
  enableMultiInstance?: boolean;
  defaultMapId?: string;
  defaultInstanceOptions?: MapInstanceInitialOptions;
}

export function MapProvider({
  children,
  defaultOptions,
  onMapReady,
  enableMultiInstance = false,
  defaultMapId,
  defaultInstanceOptions,
}: MapProviderProps) {
  const contextValue: MapConfigContextType = {
    defaultOptions,
    onMapReady,
  };

  // 🎯 다중 인스턴스 모드가 활성화된 경우 MapInstanceProvider로 감싸기
  if (enableMultiInstance) {
    return (
      <MapConfigContext.Provider value={contextValue}>
        <QueryProvider>
          <MapInstanceProvider
            mapId={defaultMapId}
            initialOptions={defaultInstanceOptions}
          >
            {children}
          </MapInstanceProvider>
        </QueryProvider>
      </MapConfigContext.Provider>
    );
  }

  // 기존 동작 (단일 지도 모드)
  return (
    <MapConfigContext.Provider value={contextValue}>
      <QueryProvider>{children}</QueryProvider>
    </MapConfigContext.Provider>
  );
}

/**
 * 전역 지도 설정에 접근하는 훅
 * MapProvider가 설정되지 않은 경우 기본값을 제공하거나 에러를 발생시킵니다.
 */
export function useMapConfig(): MapConfigContextType {
  const context = useContext(MapConfigContext);

  if (!context) {
    // 개발 환경에서는 경고 메시지 출력
    if (process.env.NODE_ENV === "development") {
      console.warn(
        "⚠️ MapProvider가 설정되지 않았습니다. 기본 설정을 사용합니다.\n" +
          "권장사항: 컴포넌트 상위에 <MapProvider>를 설정하세요.\n\n" +
          "예시:\n" +
          '<MapProvider defaultOptions={{ projection: "EPSG:5179" }}>\n' +
          "  <App />\n" +
          "</MapProvider>",
      );
    }

    // 기본값 반환 (fallback)
    return {
      defaultOptions: {
        projection: "EPSG:5186",
        center: [899587.**********, 1664175.**********],
        zoom: 11,
      },
      onMapReady: undefined,
    };
  }

  return context;
}

/**
 * MapProvider 설정 여부를 확인하는 훅
 */
export function useMapProviderStatus(): {
  isProviderSet: boolean;
  hasWarned: boolean;
} {
  const context = useContext(MapConfigContext);

  return {
    isProviderSet: !!context,
    hasWarned: !context && process.env.NODE_ENV === "development",
  };
}

/**
 * 컴포넌트 props와 전역 설정을 병합하는 유틸리티
 */
export function mergeMapOptions(
  componentProps: Partial<MapInitializeOptions>,
  defaultOptions: Partial<MapInitializeOptions>,
): MapInitializeOptions {
  return {
    ...defaultOptions,
    ...componentProps, // props가 우선권
  } as MapInitializeOptions;
}
