"use client";

import type { ReactNode } from "react";
import React, { createContext, useContext, useMemo } from "react";

import { createDrawStore } from "../stores/draw-store";
import { createEventStore } from "../stores/event-store";
import { createLayerStore } from "../stores/layer-store";
import { createMapStore } from "../stores/map-store";

/**
 * 지도 인스턴스별 초기 옵션 타입
 */
export interface MapInstanceInitialOptions {
  map?: Partial<Parameters<typeof createMapStore>[0]>;
  event?: Partial<Parameters<typeof createEventStore>[0]>;
  layer?: Partial<Parameters<typeof createLayerStore>[0]>;
  draw?: Partial<Parameters<typeof createDrawStore>[0]>;
}

/**
 * 지도 인스턴스 컨텍스트 타입
 */
export interface MapInstanceContextType {
  mapStore: ReturnType<typeof createMapStore>;
  eventStore: ReturnType<typeof createEventStore>;
  layerStore: ReturnType<typeof createLayerStore>;
  drawStore: ReturnType<typeof createDrawStore>;
  mapId: string;
  isMultiInstance: boolean;
}

// MapInstance Context 생성
const MapInstanceContext = createContext<MapInstanceContextType | null>(null);

/**
 * 지도 ID 생성 유틸리티
 */
const generateMapId = (): string => {
  return `map_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * 지도 인스턴스별 독립 스토어 Provider
 *
 * 각 지도 컴포넌트에 대해 독립된 Zustand 스토어 인스턴스들을 생성하고 제공합니다.
 *
 * @example
 * ```tsx
 * // 다중 지도 사용
 * <MapInstanceProvider mapId="map1" initialOptions={{...}}>
 *   <Map>
 *     <DrawProvider />
 *   </Map>
 * </MapInstanceProvider>
 *
 * <MapInstanceProvider mapId="map2" initialOptions={{...}}>
 *   <Map>
 *     <DrawProvider />
 *   </Map>
 * </MapInstanceProvider>
 * ```
 */
export interface MapInstanceProviderProps {
  children: ReactNode;
  mapId?: string;
  initialOptions?: MapInstanceInitialOptions;
}

export function MapInstanceProvider({
  children,
  mapId = generateMapId(),
  initialOptions = {},
}: MapInstanceProviderProps) {
  // 각 지도 인스턴스별로 독립된 스토어들을 생성
  const stores = useMemo(() => {
    console.log(`🗺️ Creating new map instance stores for: ${mapId}`);

    return {
      mapStore: createMapStore(initialOptions.map),
      eventStore: createEventStore(initialOptions.event),
      layerStore: createLayerStore(initialOptions.layer),
      drawStore: createDrawStore(initialOptions.draw),
      mapId,
      isMultiInstance: true,
    };
  }, [mapId, initialOptions]);

  return (
    <MapInstanceContext.Provider value={stores}>
      {children}
    </MapInstanceContext.Provider>
  );
}

/**
 * MapInstanceContext에 접근하는 훅
 *
 * Context가 없는 경우 null을 반환하여 전역 스토어 사용을 허용합니다.
 */
export function useMapInstanceContext(): MapInstanceContextType | null {
  return useContext(MapInstanceContext);
}

/**
 * MapInstanceContext에 반드시 접근해야 하는 훅
 *
 * Context가 없는 경우 에러를 발생시킵니다.
 */
export function useMapInstanceContextRequired(): MapInstanceContextType {
  const context = useContext(MapInstanceContext);

  if (!context) {
    throw new Error(
      "useMapInstanceContextRequired는 MapInstanceProvider 내부에서만 사용할 수 있습니다.",
    );
  }

  return context;
}

/**
 * 현재 지도의 고유 ID를 반환하는 훅
 */
export function useMapId(): string {
  const context = useMapInstanceContext();
  return context?.mapId || "global";
}

/**
 * 다중 지도 모드인지 확인하는 훅
 */
export function useIsMultiInstance(): boolean {
  const context = useMapInstanceContext();
  return context?.isMultiInstance || false;
}

/**
 * 개발자 도구: 현재 활성화된 모든 지도 인스턴스 정보
 */
export function useMapInstanceDebugInfo() {
  const context = useMapInstanceContext();

  return {
    hasContext: !!context,
    mapId: context?.mapId || "global",
    isMultiInstance: context?.isMultiInstance || false,
    storesInitialized: context
      ? {
          mapStore: !!context.mapStore,
          eventStore: !!context.eventStore,
          layerStore: !!context.layerStore,
          drawStore: !!context.drawStore,
        }
      : null,
  };
}
