import type {
  APIRequestType,
  APIResponseType,
  EstateClient,
} from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@geon-ui/react/primitives/table";
import React from "react";

export default function LandHistoryTable({
  pnu,
  crtfckey,
  numOfRows: nor,
  pageNo: pn,
  startDt: startDate = "19480501",
  endDt: endDate = "20161231",
  client,
}: APIRequestType<EstateClient["land"]["history"]> & {
  client: EstateClient;
}) {
  // TODO: Pagination States
  const [numOfRows] = React.useState<number>(nor);
  const [pageNo] = React.useState<number>(pn);
  // TODO: Date States
  const [startDt] = React.useState<string>(startDate);
  const [endDt] = React.useState<string>(endDate);

  // query
  const { data, isError, error, isLoading } = useAppQuery<
    APIResponseType<EstateClient["land"]["history"]>
  >({
    queryKey: ["land/history", pnu],
    queryFn: () =>
      client.land.history({ pnu, crtfckey, numOfRows, pageNo, startDt, endDt }),
  });

  // error handling
  if (isError)
    return (
      <div className="w-full flex justify-center align-middle">
        Error loading land data: {error as string}
      </div>
    );

  return (
    <Table className="w-full">
      <TableHeader>
        <TableRow>
          <TableHead className="font-bold text-center">순번</TableHead>
          <TableHead className="font-bold text-center">대장</TableHead>
          <TableHead className="font-bold text-center">
            토지 이동 사유
          </TableHead>
          <TableHead className="font-bold text-center">
            토지 이동 일자
          </TableHead>
          <TableHead className="font-bold text-center">
            토지 이동 말소 일자
          </TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {isLoading ? (
          // If is loading:
          <TableRow>
            <TableCell colSpan={5} className="text-center">
              Loading ...
            </TableCell>
          </TableRow>
        ) : typeof data?.result !== "string" && data?.result.resultList[0] ? (
          // If there is result:
          data.result.resultList.map((res, idx) => (
            <TableRow key={`land-history-${idx}`}>
              <TableCell className="text-center">{res.ladMvmnHistSn}</TableCell>
              <TableCell className="text-center">
                {res.regstrSeCodeNm}
              </TableCell>
              <TableCell className="text-center">
                {res.ladMvmnPrvonshCodeNm}
              </TableCell>
              <TableCell className="text-center">{res.ladMvmnDe}</TableCell>
              <TableCell className="text-center">{res.ladMvmnErsrDe}</TableCell>
            </TableRow>
          ))
        ) : (
          // If there is no result:
          <TableRow>
            <TableCell colSpan={5} className="text-center">
              No Data
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
}
