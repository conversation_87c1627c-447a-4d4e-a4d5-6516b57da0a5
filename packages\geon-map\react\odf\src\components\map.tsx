import React, { useEffect } from "react";

import { 
  MapInstanceProvider,
  useMapInstanceContext,
  type MapInstanceInitialOptions 
} from "../contexts/map-instance-context";
import { useMap } from "../hooks/use-map";
import { useMapActions } from "../hooks/use-map-actions";
import { cn } from "../lib/utils";
import type { MapInitializeOptions, UseMapReturn } from "../types/map-types";

interface MapInital extends Partial<MapInitializeOptions> {
  className?: string;
  style?: React.CSSProperties;
  children: React.ReactNode;
  containerRef?: React.RefObject<HTMLDivElement>;
  autoInit?: boolean;
  onMapInit?: (mapState: UseMapReturn) => void;
  
  // 🆕 다중 지도 인스턴스 지원 옵션들
  mapId?: string;
  enableMultiInstance?: boolean;
  initialStoreOptions?: MapInstanceInitialOptions;
}

/**
 * Map 컴포넌트 (Components 레이어)
 * useMap, useMapActions 훅을 사용하여 ODF 지도를 초기화하고 상태를 관리합니다.
 * hook 또는 해당 컴포넌트를 사용하여 지도를 렌더링할 수 있습니다.
 *
 * @example
 * ```tsx
 * // 단일 지도 (기존 방식)
 * <MapProvider defaultOptions={{ projection: 'EPSG:4326' }}>
 *   <Map className="w-full h-96" center={[127, 37]}>
 *     <DrawProvider />
 *   </Map>
 * </MapProvider>
 * 
 * // 다중 지도 인스턴스 (새로운 방식)
 * <MapProvider defaultOptions={{ projection: 'EPSG:4326' }}>
 *   <Map mapId="map1" enableMultiInstance center={[127, 37]}>
 *     <DrawProvider />
 *   </Map>
 *   <Map mapId="map2" enableMultiInstance center={[129, 35]}>
 *     <DrawProvider />
 *   </Map>
 * </MapProvider>
 * ```
 */
/**
 * 🎯 내부 지도 렌더링 컴포넌트 (MapInstanceProvider 내부에서 사용)
 */
const MapInner = ({
  className,
  style,
  children,
  onMapInit,
  center,
  zoom,
  projection,
  ...mapInitializeOptions
}: Omit<MapInital, 'mapId' | 'enableMultiInstance' | 'initialStoreOptions'>) => {
  const id = React.useId();
  const containerRef = React.useRef<HTMLDivElement>(null);

  // 초기화 완료 추적을 위한 ref (무한루프 방지)
  const initializedRef = React.useRef(false);

  // 통합 지도 훅을 통한 초기화 및 상태 관리
  const { isReady, isLoading, error } = useMap({
    containerRef,
    center,
    zoom,
    projection,
    ...mapInitializeOptions,
    onMapInit,
  });

  // 지도 조작 액션들
  const { setCenter, setZoom } = useMapActions();

  // 🎯 성능 최적화: 초기화 시에만 center/zoom 설정 (무한루프 방지)
  useEffect(() => {
    if (isReady && !initializedRef.current) {
      initializedRef.current = true;

      // 초기 center 설정 (props로 전달된 경우에만)
      if (center && setCenter) {
        setCenter(center);
      }

      // 초기 zoom 설정 (props로 전달된 경우에만)
      if (zoom !== undefined && setZoom) {
        setZoom(zoom);
      }
    }
  }, [isReady, center, zoom, setCenter, setZoom]);

  // 에러 상태 표시
  if (error) {
    return (
      <div
        className={cn("relative flex items-center justify-center", className)}
        style={style}
      >
        <div className="text-red-500">지도 로딩 실패: {error}</div>
      </div>
    );
  }

  return (
    <div className={cn("relative", className)} style={style}>
      <div id={`map-${id}`} ref={containerRef} className="w-full h-full" />
      {/* 지도가 완전히 준비된 후에만 children 렌더링 */}
      {isReady && children}
      {/* 로딩 상태 표시 (선택적) */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-75">
          <div className="text-gray-600">지도 로딩 중...</div>
        </div>
      )}
    </div>
  );
};

/**
 * 🎯 메인 Map 컴포넌트 (다중 지도 인스턴스 지원)
 * 
 * enableMultiInstance가 true인 경우 MapInstanceProvider로 감싸고,
 * false인 경우 기존 방식대로 동작합니다.
 */
export const Map = ({
  mapId,
  enableMultiInstance = false,
  initialStoreOptions,
  ...mapProps
}: MapInital) => {
  // Context가 이미 있는지 확인 (중복 Provider 방지)
  const existingContext = useMapInstanceContext();
  
  // 🎯 다중 인스턴스 모드이고 Context가 없는 경우에만 Provider로 감싸기
  if (enableMultiInstance && !existingContext) {
    return (
      <MapInstanceProvider 
        mapId={mapId}
        initialOptions={initialStoreOptions}
      >
        <MapInner {...mapProps} />
      </MapInstanceProvider>
    );
  }

  // 기존 동작 또는 이미 Context가 있는 경우
  return <MapInner {...mapProps} />;
};
