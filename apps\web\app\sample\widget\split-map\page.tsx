"use client";

import { Map, useIsMultiInstance, useMap, useMapId } from "@geon-map/react-odf";
import { useState } from "react";

import BasemapWidgetUse from "@/components/widget/basemap-widget-use";

/**
 * 🎯 지도 디버그 정보 표시 컴포넌트
 */
function MapDebugInfo({
  position = "top-left",
}: {
  position?: "top-left" | "top-right";
}) {
  const mapId = useMapId();
  const isMultiInstance = useIsMultiInstance();
  const { center, zoom, isReady } = useMap();

  const positionClasses = {
    "top-left": "top-2 left-2",
    "top-right": "top-2 right-2",
  };

  return (
    <div
      className={`absolute ${positionClasses[position]} z-50 max-w-[200px] rounded-lg bg-black/80 p-2 text-xs text-white`}
    >
      <div className="mb-1 font-bold">🗺️ {mapId}</div>
      <div className="space-y-1">
        <div>Multi: {isMultiInstance ? "✅" : "❌"}</div>
        <div>Ready: {isReady ? "✅" : "⚠️"}</div>
        <div>
          Center: [{center[0].toFixed(0)}, {center[1].toFixed(0)}]
        </div>
        <div>Zoom: {zoom}</div>
      </div>
    </div>
  );
}

/**
 * 🎯 분할 지도 페이지
 */
export default function SplitMapPage() {
  const [splitMode, setSplitMode] = useState<"single" | "dual">("single");

  return (
    <div className="flex h-screen flex-col">
      {/* 헤더 - 분할 모드 제어 */}
      <div className="flex items-center justify-between bg-gray-800 p-4 text-white">
        <h1 className="text-xl font-bold">🗺️ 분할 지도 테스트</h1>

        {/* 분할 모드 스위치 */}
        <div className="flex items-center gap-4">
          <span className="text-sm">분할 모드:</span>
          <div className="flex rounded-lg bg-gray-700 p-1">
            <button
              onClick={() => setSplitMode("single")}
              className={`rounded-md px-4 py-2 transition-colors ${
                splitMode === "single"
                  ? "bg-blue-600 text-white"
                  : "text-gray-300 hover:bg-gray-600"
              }`}
            >
              1분할
            </button>
            <button
              onClick={() => setSplitMode("dual")}
              className={`rounded-md px-4 py-2 transition-colors ${
                splitMode === "dual"
                  ? "bg-blue-600 text-white"
                  : "text-gray-300 hover:bg-gray-600"
              }`}
            >
              2분할
            </button>
          </div>
        </div>
      </div>

      {/* 지도 영역 */}
      <div className="relative flex-1">
        {splitMode === "single" ? (
          /* 1분할 모드 - 기존 방식 (전역 스토어) */
          <Map className="h-full w-full">
            <MapDebugInfo position="top-left" />
            <BasemapWidgetUse />
            <div className="absolute bottom-4 left-4 rounded bg-blue-600 px-3 py-1 text-white">
              단일 지도 (전역 스토어)
            </div>
          </Map>
        ) : (
          /* 2분할 모드 - 다중 인스턴스 */
          <div className="flex h-full w-full">
            {/* 왼쪽 지도 */}
            <div className="relative flex-1 border-r">
              <Map
                mapId="left-map"
                enableMultiInstance={true}
                className="h-full w-full"
                center={[953977, 1952062]} // 서울
                zoom={10}
                initialStoreOptions={{
                  map: {
                    center: [953977, 1952062],
                    zoom: 10,
                  },
                }}
              >
                <MapDebugInfo position="top-left" />
                <BasemapWidgetUse />
                <div className="absolute bottom-4 left-4 rounded bg-green-600 px-3 py-1 text-white">
                  왼쪽 지도 - 서울
                </div>
              </Map>
            </div>

            {/* 오른쪽 지도 */}
            <div className="relative flex-1">
              <Map
                mapId="right-map"
                enableMultiInstance={true}
                className="h-full w-full"
                center={[1102576, 1716026]} // 부산
                zoom={11}
                initialStoreOptions={{
                  map: {
                    center: [1102576, 1716026],
                    zoom: 11,
                  },
                }}
              >
                <MapDebugInfo position="top-right" />
                <BasemapWidgetUse />
                <div className="absolute bottom-4 right-4 rounded bg-red-600 px-3 py-1 text-white">
                  오른쪽 지도 - 부산
                </div>
              </Map>
            </div>
          </div>
        )}
      </div>

      {/* 하단 상태 정보 */}
      <div className="bg-gray-100 p-3">
        <div className="mx-auto max-w-4xl">
          <h3 className="mb-2 font-semibold text-gray-800">테스트 방법:</h3>
          <div className="space-y-1 text-sm text-gray-600">
            <div>
              • <span className="font-medium">1분할</span>: 기존 전역 스토어
              방식, 배경지도 변경 시 전역 상태 변경
            </div>
            <div>
              • <span className="font-medium">2분할</span>: 독립적인 지도
              인스턴스, 각 지도가 서로 다른 배경지도 설정 가능
            </div>
            <div>
              • 각 지도의 배경지도 위젯으로 서로 다른 배경지도를 설정해서 독립성
              확인
            </div>
            <div>• 디버그 정보에서 Multi Instance 상태와 Map ID 확인</div>
          </div>
        </div>
      </div>
    </div>
  );
}
