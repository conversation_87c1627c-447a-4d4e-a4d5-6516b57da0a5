import {
  Basemap<PERSON><PERSON>ider,
  <PERSON><PERSON><PERSON>ider,
  ControlsConfigProvider,
  Controls<PERSON><PERSON>ider,
  <PERSON><PERSON><PERSON><PERSON>,
  Map<PERSON><PERSON>ider,
  <PERSON><PERSON>ure<PERSON><PERSON><PERSON>,
  OverviewProvider,
  ScaleProvider,
} from "@geon-map/react-odf";
import React from "react";

export default function SplitMapLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <MapProvider
      defaultOptions={{
        projection: "EPSG:5186",
        center: [899587.**********, 1664175.**********], // 무안 기본값
        zoom: 8,
      }}
      enableMultiInstance={true} // 🆕 다중 인스턴스 활성화
    >
      {/* 🆕 Controls Config Provider로 설정 관리 */}
      <ControlsConfigProvider>
        {/* 🆕 통합 Controls Provider */}
        <ControlsProvider>
          {/* Config 전용 Provider들 - 설정만 전달 */}
          <ScaleProvider scaleOptions={{ size: 100, scaleInput: false }} />
          <BasemapProvider />
          <ClearProvider />
          <MeasureProvider
            measureOptions={{
              tools: ["distance", "area", "round", "spot"],
              continuity: false,
            }}
          />
          <OverviewProvider />
          <DrawProvider
            drawOptions={{
              continuity: false,
              tools: [
                "text",
                "polygon",
                "lineString",
                "box",
                "point",
                "circle",
                "curve",
              ],
              style: {
                fill: { color: [255, 255, 0, 1] },
                stroke: { color: [0, 255, 0, 0.8], width: 5 },
              },
            }}
          />
          {children}
        </ControlsProvider>
      </ControlsConfigProvider>
    </MapProvider>
  );
}
