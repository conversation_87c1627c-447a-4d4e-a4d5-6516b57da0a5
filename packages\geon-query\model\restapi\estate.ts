import type { API_TYPE, APIConfig, ResponseCode } from "../utils/geonAPI";
import { apiHelper } from "../utils/geonAPI";

const type: API_TYPE = "estate";

type EstateAPIRequest = {
  /** 검색 건 수 */
  numOfRows: number;
  /** 페이지 번호 */
  pageNo: number;
  /** 필지 고유 번호 */
  pnu: string;
  /** 인증키 */
  crtfckey?: string;
};

type EstateAPIResponse<T = any> = {
  code: ResponseCode;
  message: string;
  result:
    | string
    | {
        resultList: T[];
        pageInfo?: {
          numOfRows: number;
          pageNo: number;
          totalCount: string;
        };
        errorInfo?: {
          resultCode: string;
          message: string;
        };
        [key: string]: any;
      };
};

interface EstateAPIConfig extends APIConfig {}

export type EstateClient = ReturnType<typeof createEstateClient>;

export function createEstateClient(config: EstateAPIConfig = {}) {
  const { baseUrl, crtfckey } = config;

  const api = apiHelper<EstateAPIRequest, EstateAPIResponse>({
    type,
    baseUrl,
    crtfckey,
  });

  return {
    /** 건물 조회(일필지) */
    building: {
      /** (Open-API) 건물 층 수 조회 */
      floor: api.get<
        EstateAPIRequest & {
          /** 대지권 일련번호 */
          agbldgSn?: string;
          /** 동 이름 */
          buldDongNm?: string;
          /** 층 이름 */
          buldFloorNm?: string;
        }
      >("/api/builds/build/heading/floor"),
      /** (Open-API) 건물 호 수 조회 */
      ho: api.get<
        EstateAPIRequest & {
          /** 대지권 일련번호 */
          agbldgSn?: string;
          /** 동 이름 */
          buldDongNm?: string;
          /** 층 이름 */
          buldFloorNm?: string;
          /** 호 이름 */
          buldHoNm?: string;
        }
      >("/api/builds/build/heading/ho"),
      /** (Open-API) 대지권 등록 목록 조회 */
      register: api.get("/builds/build/register"),
      /** (Open-API) 건물일련번호 조회 */
      serial: api.get<
        EstateAPIRequest & {
          /** 대지권 일련 번호 */
          agbldgSn?: string;
        }
      >("/api/builds/build/serial"),
    },
    /** 건축물 대장 정보 조회 */
    registry: {
      /** 층 별 개요 조회 */
      floor: api.get("/api/registry/floor"),
      /** 총괄표제부 조회 */
      general: api.get("/api/registry/general/headings"),
      /** 표제부 조회 */
      headings: api.get("/api/registry/general/headings"),
      /** 전유부 조회 */
      ownership: api.get("/api/registry/ownership"),
      /** 전유공유면적 조회 */
      area: api.get<
        EstateAPIRequest & {
          /** 동 이름 */
          dongNm?: string;
          /** 호 이름 */
          hoNm?: string;
        }
      >("/registry/ownership/area"),
    },
    /** 토지 조회(일필지) */
    land: {
      /** (Open-API) 토지 임야 목록 조회 */
      basic: api.get<EstateAPIRequest>("/api/land/basic"),
      /** (Open-API) 토지 특성 속성 조회 */
      characteristics: api.get("/api/land/characteristics"),
      /** (Open-API) 토지 이동 이력 속성 조회 */
      history: api.get<
        EstateAPIRequest & {
          /** 토지 이동 일자 시작일 */
          startDt?: string;
          /** 토지 이동 일자 종료일 */
          endDt?: string;
        }
      >("/api/land/history"),
      /** (Open-API) 토지 소유 정보 속성 조회 */
      ownership: api.get("/api/land/ownership"),
      /** (Open-API) 토지이용계획 속성 조회 */
      useplan: api.get<
        EstateAPIRequest & {
          /** 저촉여부코드 */
          cnflcAt?: string;
          /** 용도지역지구 이름 */
          prposAreaDstrcCodeNm?: string;
        }
      >("/api/land/useplan"),
    },
    /** 일필지 종합 정보 */
    parcel: {
      /** (Open-API) 일필지 종합 정보 조회 */
      all: api.get<Pick<EstateAPIRequest, "crtfckey" | "pnu">>(
        "/api/parcel/all",
      ),
    },
    /** 가격 정보 조회(일필지) */
    price: {
      /** (V-World) 공동주택가격 속성 조회 */
      apt: api.get<
        EstateAPIRequest & {
          /** 동명 */
          dongNm?: string;
          /** 호명 */
          hoNm?: string;
          /** 기준 연도 */
          stdrYear?: string;
        }
      >("/api/price/house/apt"),
      /** (V-World) 개별주택가격 속성 조회 */
      ind: api.get<
        EstateAPIRequest & {
          /** 기준 연도 */
          stdrYear?: string;
        }
      >("/api/price/house/ind"),
      /** (V-World) 개별공시지가 속성 조회 */
      pclnd: api.get<
        EstateAPIRequest & {
          /** 기준 연도 */
          stdrYear?: string;
        }
      >("/api/price/pclnd"),
    },
  };
}
