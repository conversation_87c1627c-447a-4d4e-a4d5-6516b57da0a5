"use client";

import React, { useEffect } from "react";

import { useMapInstanceContext } from "../contexts/map-instance-context";
import { CoreInstanceManager } from "../stores/core-instances";
import { useDrawStore } from "../stores/draw-store";
import { useLayerStore } from "../stores/layer-store";
import { useMapStore } from "../stores/map-store";
import type { DrawControlOptions } from "../types/draw-types";

// 측정 옵션 타입은 core에서 직접 관리하므로 any로 수용
type MeasureControlOptions = any;

/**
 * 통합 Controls Provider 설정 옵션
 */
export interface ControlsProviderOptions {
  /** Draw Control 초기화 옵션 */
  drawOptions?: DrawControlOptions;
  /** Measure Control 초기화 옵션 */
  measureOptions?: MeasureControlOptions;
  /** Clear Control 초기화 옵션 */
  clearOptions?: {
    clearAll?: boolean;
  };
  /** Basemap Control 초기화 옵션 */
  basemapOptions?: {
    basemapList?: any;
    urls?: any;
  };
  /** Scale Control 초기화 옵션 */
  scaleOptions?: {
    size?: number;
    scaleInput?: boolean;
  };
  /** Overview Control 초기화 옵션 */
  overviewOptions?: {
    enabled?: boolean;
  };
  /** 자동 초기화 여부 (기본: true) */
  autoInitialize?: boolean;
  /** 에러 발생 시 콜백 */
  onError?: (error: Error) => void;
}

/**
 * 🎯 ControlsProvider (모든 Control 통합 관리)
 *
 * 모든 Control들을 통합하여 초기화하는 Provider입니다.
 * 다중 지도 인스턴스 환경에서 올바른 Store에 Control을 저장합니다.
 *
 * @example
 * ```tsx
 * <MapProvider enableMultiInstance>
 *   <ControlsProvider
 *     drawOptions={{ tools: ["polygon", "point"] }}
 *     measureOptions={{ tools: ["distance", "area"] }}
 *     basemapOptions={{ basemapList: [...] }}
 *   >
 *     <Map mapId="map1" />
 *   </ControlsProvider>
 * </MapProvider>
 * ```
 */
export function ControlsProvider({
  children,
  drawOptions = {
    continuity: false,
    createNewLayer: false,
    tools: ["text", "polygon", "lineString", "box", "point", "circle", "curve"],
  },
  measureOptions = {
    tools: ["distance", "area", "round", "spot"],
    continuity: false,
    rightClickDelete: false,
  },
  clearOptions = { clearAll: true },
  basemapOptions = {},
  scaleOptions = { size: 100, scaleInput: false },
  overviewOptions = { enabled: true },
  autoInitialize = true,
  onError,
}: React.PropsWithChildren<ControlsProviderOptions>) {
  // 🎯 Context 기반 Store 선택 (다중 지도 인스턴스 지원)
  const mapInstanceContext = useMapInstanceContext();

  // Store 선택: Context가 있으면 Context Store, 없으면 전역 Store
  const mapStore = mapInstanceContext?.mapStore || useMapStore;
  const drawStore = mapInstanceContext?.drawStore || useDrawStore;
  const layerStore = mapInstanceContext?.layerStore || useLayerStore;

  // Store 상태 구독
  const map = mapStore((state) => state.map);
  const odf = mapStore((state) => state.odf);
  const isLoading = mapStore((state) => state.isLoading);

  // Store Actions
  const setDrawCore = drawStore((state) => state.setDrawCore);
  const setMeasureCore = drawStore((state) => state.setMeasureCore);
  const setClearCore = drawStore((state) => state.setClearCore);
  const setBasemapInstance = mapStore((state) => state.setBasemapInstance);
  const setScaleInstance = mapStore((state) => state.setScaleInstance);
  const setOverviewInstance = mapStore((state) => state.setOverviewInstance);

  useEffect(() => {
    if (!autoInitialize) return;

    // Map이 준비되면 모든 Control들을 초기화
    if (map && odf && !isLoading) {
      initializeAllControls();
    }

    // 컴포넌트 언마운트 시 정리
    return () => {
      cleanupAllControls();
    };
  }, [map, odf, isLoading, autoInitialize]);

  /**
   * 모든 Control들을 초기화하는 함수
   */
  const initializeAllControls = async () => {
    const errors: string[] = [];

    try {
      // 1. Draw Control 초기화
      if (drawOptions) {
        await initializeDrawControl(errors);
      }

      // 2. Measure Control 초기화
      if (measureOptions) {
        await initializeMeasureControl(errors);
      }

      // 3. Clear Control 초기화
      if (clearOptions) {
        await initializeClearControl(errors);
      }

      // 4. Basemap Control 초기화
      if (basemapOptions) {
        await initializeBasemapControl(errors);
      }

      // 5. Scale Control 초기화
      if (scaleOptions) {
        await initializeScaleControl(errors);
      }

      // 6. Overview Control 초기화
      if (overviewOptions?.enabled) {
        await initializeOverviewControl(errors);
      }

      // 에러가 있으면 콜백 호출
      if (errors.length > 0) {
        const error = new Error(
          `Controls 초기화 실패: ${errors.join(", ")}`
        );
        console.error("❌ Controls initialization failed:", errors);
        onError?.(error);
      }
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      console.error("❌ Failed to initialize controls:", err);
      onError?.(err);
    }
  };

  /**
   * Draw Control 초기화
   */
  const initializeDrawControl = async (errors: string[]) => {
    try {
      const { drawCore, errors: drawErrors } = CoreInstanceManager.createDrawCore(
        map,
        odf,
        finalOptions.drawOptions,
      );

      if (drawCore) {
        setDrawCore(drawCore);

        // Provider 스타일을 Store와 동기화
        try {
          const style = (finalOptions.drawOptions as any)?.style;
          if (style) {
            drawStore.getState().setDrawStyle(style);
          }
        } catch (e) {
          console.error(e);
        }

        // LayerStore에 Draw 레이어 등록
        const drawLayer = drawCore.getDrawLayer();
        if (drawLayer) {
          layerStore.getState().addDrawLayer(drawLayer, {
            name: "Draw Layer",
            visible: true,
          });
        }
      }

      errors.push(...drawErrors);
    } catch (error) {
      errors.push(`Draw Control 초기화 실패: ${error}`);
    }
  };

  /**
   * Measure Control 초기화
   */
  const initializeMeasureControl = async (errors: string[]) => {
    try {
      const { measureCore, errors: measureErrors } =
        CoreInstanceManager.createMeasureOnlyCores(map, odf, finalOptions.measureOptions);

      if (measureCore) {
        setMeasureCore(measureCore);

        // Provider 스타일을 Store와 동기화
        try {
          const style = (finalOptions.measureOptions as any)?.style;
          if (style) {
            drawStore.getState().setMeasureStyle(style);
          }
        } catch (e) {
          console.error(e);
        }

        // LayerStore에 Measure 레이어 등록
        const measureLayer = measureCore.getMeasureLayer();
        if (measureLayer) {
          layerStore.getState().addMeasureLayer(measureLayer, {
            name: "Measure Layer",
            visible: true,
          });
        }
      }

      errors.push(...measureErrors);
    } catch (error) {
      errors.push(`Measure Control 초기화 실패: ${error}`);
    }
  };

  /**
   * Clear Control 초기화
   */
  const initializeClearControl = async (errors: string[]) => {
    try {
      const { clearCore, errors: clearErrors } = CoreInstanceManager.createClearOnlyCores(
        map,
        odf,
        finalOptions.clearOptions?.clearAll ?? true,
      );

      if (clearCore) {
        setClearCore(clearCore);
      }

      errors.push(...clearErrors);
    } catch (error) {
      errors.push(`Clear Control 초기화 실패: ${error}`);
    }
  };

  /**
   * Basemap Control 초기화
   */
  const initializeBasemapControl = async (errors: string[]) => {
    try {
      const { basemapInstance, errors: basemapErrors } =
        CoreInstanceManager.createBasemapCores(map, odf, finalOptions.basemapOptions);

      if (basemapInstance) {
        setBasemapInstance(basemapInstance);
      }

      errors.push(...basemapErrors);
    } catch (error) {
      errors.push(`Basemap Control 초기화 실패: ${error}`);
    }
  };

  /**
   * Scale Control 초기화
   */
  const initializeScaleControl = async (errors: string[]) => {
    try {
      const { scaleInstance, errors: scaleErrors } = CoreInstanceManager.createScaleCores(
        map,
        odf,
        finalOptions.scaleOptions,
      );

      if (scaleInstance) {
        setScaleInstance(scaleInstance);
      }

      errors.push(...scaleErrors);
    } catch (error) {
      errors.push(`Scale Control 초기화 실패: ${error}`);
    }
  };

  /**
   * Overview Control 초기화
   */
  const initializeOverviewControl = async (errors: string[]) => {
    try {
      const { overviewCore, errors: overviewErrors } = CoreInstanceManager.createOverviewCore(
        map,
        odf,
      );

      if (overviewCore) {
        setOverviewInstance(overviewCore);
      }

      errors.push(...overviewErrors);
    } catch (error) {
      errors.push(`Overview Control 초기화 실패: ${error}`);
    }
  };

  /**
   * 모든 Control들을 정리하는 함수
   */
  const cleanupAllControls = () => {
    try {
      setDrawCore(null);
      setMeasureCore(null);
      setClearCore(null);
      setBasemapInstance(null);
      setScaleInstance(null);
      setOverviewInstance(null);
    } catch (error) {
      console.error("❌ Failed to cleanup controls:", error);
    }
  };

  return <>{children}</>;
}
