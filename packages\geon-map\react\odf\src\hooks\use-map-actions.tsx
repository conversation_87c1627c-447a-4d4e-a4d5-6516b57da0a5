import { BasemapInfo, LayerFactory } from "@geon-map/core";
import { useCallback } from "react";

import { useMapInstanceContext } from "../contexts/map-instance-context";
import { useMapStore } from "../stores/map-store";

declare const odf: any;

/**
 * 🎯 지도 조작 액션들을 제공하는 훅
 *
 * ✅ Store Actions 패턴 사용
 * Hook → Store Actions → Core 인스턴스 조작
 * 깔끔하고 일관된 API 제공
 */
export function useMapActions() {
  // 🎯 Context 기반 스토어 선택 (다중 지도 인스턴스 지원)
  const mapInstanceContext = useMapInstanceContext();
  const mapStore = mapInstanceContext?.mapStore || useMapStore;

  // ✅ Store Actions 가져오기 (Context 기반 또는 전역 스토어 사용)
  const setCenter = mapStore((state) => state.setCenter);
  const setZoom = mapStore((state) => state.setZoom);
  const panTo = mapStore((state) => state.panTo);
  const moveToCurrentLocation = mapStore(
    (state) => state.moveToCurrentLocation,
  );

  const map = mapStore((state) => state.map);

  // Store Actions에서 베이스맵 액션들 가져오기
  const switchBasemap = mapStore((state) => state.switchBasemap);
  const getCurrentBasemap = mapStore((state) => state.getCurrentBasemap);
  const getAvailableBasemaps = mapStore((state) => state.getAvailableBasemaps);

  // 베이스맵 변경 (Store Actions 사용)
  const setBasemap = useCallback(
    (basemapId: string) => {
      switchBasemap(basemapId);
    },
    [switchBasemap],
  );

  // 베이스맵 정보 설정
  const setBasemapInfo = useCallback(
    (basemapInfo: BasemapInfo) => {
      if (!map) return;
      const layerFactory = new LayerFactory(map, odf);
      layerFactory.setBasemapInfo(basemapInfo);
    },
    [map],
  );

  // 🎯 성능 최적화: 좌표 변환 유틸리티 (Context 기반)
  const transformCoordinate = useCallback(
    (coords: [number, number], sourceProjection: string): [number, number] => {
      // Context 기반 스토어에서 mapInstance 가져오기
      const currentMapInstance =
        mapInstanceContext?.mapStore?.getState?.().mapInstance ||
        useMapStore.getState().mapInstance;

      if (!currentMapInstance) {
        console.warn("MapController가 초기화되지 않았습니다.");
        return coords;
      }

      try {
        return currentMapInstance.transformCoordinate(coords, sourceProjection);
      } catch (error) {
        console.error("좌표 변환 실패:", error);
        return coords;
      }
    },
    [mapInstanceContext], // Context 의존성 추가
  );

  return {
    // Store Actions (권장 사용법)
    setCenter,
    setZoom,
    panTo,
    moveToCurrentLocation,

    // 베이스맵 관련 (Store Actions 기반)
    setBasemap,
    getCurrentBasemap,
    getAvailableBasemaps,
    setBasemapInfo,

    // 유틸리티 함수
    transformCoordinate,

    // 고급 사용자용 (직접 접근) - Context 기반 최신 상태 접근
    get mapInstance() {
      return (
        mapInstanceContext?.mapStore?.getState?.().mapInstance ||
        useMapStore.getState().mapInstance
      );
    },
  };
}
